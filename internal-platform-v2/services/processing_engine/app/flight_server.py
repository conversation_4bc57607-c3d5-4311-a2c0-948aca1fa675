"""Arrow Flight server for TerraFlow processing engine.
Handles data streaming between workers and drivers using Apache Arrow Flight.
"""
import logging
import os
import threading
import time
import uuid
from collections import defaultdict
from typing import Dict, Any, Optional, List, Tuple

import pyarrow as pa
import pyarrow.flight as flight
from pyarrow.flight import FlightServerBase, ServerCallContext

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Shared caches and lock from driver
LOCAL_FLIGHT_RESULT_CACHE = {}
LOCAL_FLIGHT_STATUS_CACHE = {}
FLIGHT_LOCK = threading.Lock()

class ExecutionState:
    """Tracks the state of an execution including data and metadata."""
    
    def __init__(self, execution_id: str):
        self.execution_id = execution_id
        self.batches: List[pa.RecordBatch] = []
        self.schema: Optional[pa.Schema] = None
        self.status: str = "PENDING"
        self.start_time: float = time.time()
        self.end_time: Optional[float] = None
        self.metadata: Dict[str, Any] = {}
        self.error: Optional[str] = None
        self.rows_processed: int = 0
        self.batches_received: int = 0
    
    def add_batch(self, batch: pa.RecordBatch) -> None:
        """Add a batch to this execution."""
        if self.schema is None:
            self.schema = batch.schema
        elif self.schema != batch.schema:
            raise ValueError("Schema mismatch in batches for the same execution")
            
        self.batches.append(batch)
        self.rows_processed += batch.num_rows
        self.batches_received += 1
        self.status = "PROCESSING"
    
    def complete(self, error: Optional[str] = None) -> None:
        """Mark this execution as complete."""
        self.status = "FAILED" if error else "COMPLETED"
        self.error = error
        self.end_time = time.time()
    
    def to_table(self) -> Optional[pa.Table]:
        """Convert all batches to a single table."""
        if not self.batches:
            return None
        return pa.Table.from_batches(self.batches)
    
    def to_status_dict(self) -> Dict[str, Any]:
        """Convert to a status dictionary for the status cache."""
        return {
            "status": self.status,
            "details": {
                "start_time": int(self.start_time * 1000),
                "end_time": int(self.end_time * 1000) if self.end_time else None,
                "rows_processed": self.rows_processed,
                "batches_received": self.batches_received,
                "error": self.error,
                **self.metadata
            }
        }

class FlightServer(FlightServerBase):
    """
    Arrow Flight server for TerraFlow processing engine.
    Handles data streaming between workers and drivers with proper batch accumulation.
    """

    # Class-level lock for synchronization
    _sync_lock = threading.RLock()

    def __init__(self, location):
        super().__init__(location=location)
        self._executions: Dict[str, ExecutionState] = {}
        self._lock = threading.RLock()  # Reentrant lock for nested operations
        self._ttl_seconds = int(os.getenv("FLIGHT_CACHE_TTL_SECONDS", "3600"))  # 1 hour default TTL
        
        # Start cleanup thread
        self._cleanup_interval = min(300, self._ttl_seconds // 2)  # Cleanup interval, max 5 min
        self._shutdown_event = threading.Event()
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="FlightCacheCleanup",
            daemon=True
        )
        self._cleanup_thread.start()
        logger.info(f"Started Flight server at {location} with TTL: {self._ttl_seconds}s")
    
    def _cleanup_loop(self):
        """Background thread that periodically cleans up old executions."""
        while not self._shutdown_event.is_set():
            try:
                current_time = time.time()
                expired = []
                
                with self._lock:
                    # Find expired executions
                    for exec_id, exec_state in list(self._executions.items()):
                        # Use end_time if available, otherwise use start_time
                        last_active = exec_state.end_time or exec_state.start_time
                        if (current_time - last_active) > self._ttl_seconds:
                            expired.append(exec_id)
                    
                    # Remove expired executions
                    for exec_id in expired:
                        if exec_id in self._executions:
                            exec_state = self._executions.pop(exec_id)
                            logger.info(
                                f"Cleaned up execution {exec_id} with status {exec_state.status} "
                                f"({exec_state.rows_processed} rows, {exec_state.batches_received} batches)"
                            )
                
                if expired:
                    logger.info(f"Cleaned up {len(expired)} expired executions")
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}", exc_info=True)
            
            # Wait for the next cleanup interval or until shutdown
            self._shutdown_event.wait(self._cleanup_interval)
    
    def _get_or_create_execution(self, execution_id: str) -> ExecutionState:
        """Get an existing execution or create a new one if it doesn't exist.
        
        Handles both prefixed (ticket_for_<id>) and non-prefixed execution IDs.
        The execution is always stored with the base ID (without ticket_for_ prefix).
        """
        # Normalize the ID by removing the ticket_for_ prefix if present
        base_id = execution_id[11:] if execution_id.startswith('ticket_for_') else execution_id
        
        with self._lock:
            if base_id not in self._executions:
                self._executions[base_id] = ExecutionState(base_id)
                logger.info(f"Created new execution: {base_id}")
            return self._executions[base_id]
    
    def _get_execution(self, execution_id: str) -> ExecutionState:
        """Get an existing execution or raise FlightError with NOT_FOUND status.
        
        Handles both prefixed (ticket_for_<id>) and non-prefixed execution IDs.
        """
        with self._lock:
            # First try with the exact ID
            if execution_id in self._executions:
                return self._executions[execution_id]
                
            # If not found, try stripping the 'ticket_for_' prefix if present
            if execution_id.startswith('ticket_for_'):
                base_id = execution_id[11:]  # Remove 'ticket_for_' prefix
                if base_id in self._executions:
                    return self._executions[base_id]
            # Also try adding the prefix if it's not there
            elif f'ticket_for_{execution_id}' in self._executions:
                return self._executions[f'ticket_for_{execution_id}']
                
            # If we get here, the execution was not found
            error_msg = f"Execution {execution_id} not found"
            try:
                # Try the newer way first (pyarrow >= 8.0.0)
                raise flight.FlightUnavailableError(
                    error_msg,
                    status_code=2  # NOT_FOUND
                )
            except (AttributeError, TypeError):
                # Fallback for older pyarrow versions
                raise flight.FlightUnavailableError(error_msg)
            
    def shutdown(self):
        """Shut down the server and cleanup resources."""
        logger.info("Shutting down Flight server...")
        self._shutdown_event.set()
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5.0)
        super().shutdown()

    def do_put(self, context, descriptor, reader, writer):
        """Handle data upload from workers.
        
        This method accumulates RecordBatches for each execution and updates the execution state.
        """
        execution_id = descriptor.command.decode("utf-8")
        logger.info(f"Received do_put request for execution: {execution_id}")
        
        try:
            # Get or create execution state
            exec_state = self._get_or_create_execution(execution_id)
            
            # Process the data stream
            total_batches = 0
            total_rows = 0
            
            while True:
                try:
                    # Read the next batch from the client
                    flight_batch = reader.read_chunk()
                    if flight_batch is None:
                        break
                        
                    # Add the batch to our execution
                    batch = flight_batch.data
                    exec_state.add_batch(batch)
                    
                    total_batches += 1
                    total_rows += batch.num_rows
                    
                    # Log progress periodically
                    if total_batches % 10 == 0 or total_batches == 1:
                        logger.info(
                            f"Processed {total_batches} batches ({total_rows} rows) "
                            f"for execution {execution_id}"
                        )
                        
                except StopIteration:
                    break
                except Exception as e:
                    error_msg = f"Error processing batch {total_batches + 1} for {execution_id}: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    exec_state.complete(error=error_msg)
                    try:
                        # Try the newer way first (pyarrow >= 8.0.0)
                        raise flight.FlightInternalError(error_msg, status_code=13)  # INTERNAL
                    except (AttributeError, TypeError):
                        # Fallback for older pyarrow versions
                        raise flight.FlightInternalError(error_msg)
            
            # Mark execution as complete
            if exec_state.status != "FAILED":
                exec_state.complete()
                logger.info(
                    f"Completed upload for execution {execution_id}: "
                    f"{total_batches} batches, {total_rows} rows"
                )
            
            # Update the global caches
            with FLIGHT_LOCK:
                LOCAL_FLIGHT_STATUS_CACHE[execution_id] = exec_state.to_status_dict()
                if exec_state.status == "COMPLETED":
                    LOCAL_FLIGHT_RESULT_CACHE[execution_id] = exec_state.to_table()
            
            # For Flight protocol, we don't need to return anything here
            # The writer is already being used to stream the data
            return None
            
        except Exception as e:
            error_msg = f"Error in do_put for execution {execution_id}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # Update execution state with error
            try:
                exec_state = self._get_or_create_execution(execution_id)
                exec_state.complete(error=error_msg)
                with FLIGHT_LOCK:
                    LOCAL_FLIGHT_STATUS_CACHE[execution_id] = exec_state.to_status_dict()
            except Exception as inner_e:
                logger.error(f"Error updating execution status: {str(inner_e)}", exc_info=True)
            
            try:
                # Try the newer way first (pyarrow >= 8.0.0)
                raise flight.FlightInternalError(error_msg, status_code=13)  # INTERNAL
            except (AttributeError, TypeError):
                # Fallback for older pyarrow versions
                raise flight.FlightInternalError(error_msg)
    
    def do_get(self, context, ticket: flight.Ticket) -> flight.FlightDataStream:
        """Synchronous handler for Flight DoGet requests."""
        ticket_str = ticket.ticket.decode("utf-8")
        execution_id = ticket_str[len("ticket_for_"):]

        logger.info(f"[SYNC] Received Flight do_get request for ticket: {ticket_str}")
        logger.info(f"[SYNC] Current cache keys: {list(LOCAL_FLIGHT_STATUS_CACHE.keys())}")
        logger.info(f"[SYNC] Current result cache keys: {list(LOCAL_FLIGHT_RESULT_CACHE.keys())}")

        # Protect cache access
        with FlightServer._sync_lock:
            # Ensure there's always a cache entry
            if execution_id not in LOCAL_FLIGHT_STATUS_CACHE:
                logger.info(f"[SYNC] Creating placeholder for new execution: {execution_id}")
                import time
                now = int(time.time() * 1000)
                LOCAL_FLIGHT_STATUS_CACHE[execution_id] = {
                    "status": "PENDING",
                    "details": {"start_time": now, "operations": []}
                }
            # Now fetch the (possibly new) status and any results
            status_info  = LOCAL_FLIGHT_STATUS_CACHE[execution_id]
            result_table = LOCAL_FLIGHT_RESULT_CACHE.get(execution_id)

        # At this point status_info is never None
        status = status_info.get("status")

        if status == "COMPLETED":
            # Handle both legacy table format and new batch list format
            if isinstance(result_table, list):
                # New format: list of batches from workers
                if not result_table:
                    logger.warning(f"[SYNC] No batches found for completed job {execution_id}")
                    # Return empty table with basic schema
                    empty_table = pa.Table.from_arrays([], names=[])
                    return flight.RecordBatchStream(empty_table)

                # Combine all batches into a single table
                try:
                    final_table = pa.Table.from_batches(result_table)
                    logger.info(
                        f"[SYNC] Job {execution_id} completed. Streaming "
                        f"{final_table.num_rows} rows from {len(result_table)} batches."
                    )
                    return flight.RecordBatchStream(final_table)
                except Exception as e:
                    logger.error(f"[SYNC] Failed to combine batches for {execution_id}: {e}")
                    raise flight.FlightInternalError(f"Failed to combine result batches: {e}")

            elif isinstance(result_table, pa.Table):
                # Legacy format: single table
                logger.info(
                    f"[SYNC] Job {execution_id} completed. Streaming "
                    f"{result_table.num_rows} rows."
                )
                return flight.RecordBatchStream(result_table)
            else:
                logger.error(
                    f"[SYNC] Cached result for {execution_id} is not a pa.Table or list "
                    f"(Type: {type(result_table)})."
                )
                raise flight.FlightInternalError(
                    f"No data available for execution {execution_id}"
                )

        elif status == "FAILED":
            error_details = status_info.get("details", {}).get("error", "Unknown")
            logger.warning(f"[SYNC] Job {execution_id} failed: {error_details}")
            raise flight.FlightInternalError(f"Job failed: {error_details}")

        else:
            # Any non-complete status
            raise flight.FlightUnavailableError(
                f"Job {execution_id} is not complete yet (Status: {status})"
            )



    async def list_flights(self, context, criteria):
        """Return FlightInfo entries for completed jobs."""
        for exec_id, status in LOCAL_FLIGHT_STATUS_CACHE.items():
            if status.get("status") == "COMPLETED":
                table = LOCAL_FLIGHT_RESULT_CACHE.get(exec_id)
                schema = table.schema
                ticket = flight.Ticket(f"ticket_for_{exec_id}".encode("utf-8"))
                endpoint = flight.FlightEndpoint(ticket, [self.location])
                info = flight.FlightInfo(
                    schema=schema,
                    descriptor=flight.FlightDescriptor.for_path(exec_id),
                    endpoint=[endpoint],
                    total_records=table.num_rows,
                    total_bytes=table.nbytes
                )
                yield info

    async def get_flight_info(self, context, descriptor):
        """Return FlightInfo for a single execution."""
        path = descriptor.path or []
        if not path:
            raise flight.FlightInvalidArgument("No execution ID provided.")
        execution_id = path[0]
        try:
            status = LOCAL_FLIGHT_STATUS_CACHE.get(execution_id, {})
            table = LOCAL_FLIGHT_RESULT_CACHE.get(execution_id)
            if status.get("status") == "COMPLETED" and table is not None:
                ticket = flight.Ticket(f"ticket_for_{execution_id}".encode("utf-8"))
                endpoint = flight.FlightEndpoint(ticket, [self.location])

                return flight.FlightInfo(
                    schema=table.schema,
                    descriptor=descriptor,
                    endpoint=[endpoint],
                    total_records=table.num_rows,
                    total_bytes=table.nbytes
                )
            else:
                raise flight.FlightUnavailableError(f"Execution {execution_id} not found or not completed")

        except flight.FlightError:
            raise  # Re-raise Flight-specific errors
        except Exception as e:
            error_msg = f"Error in get_flight_info for {execution_id}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            try:
                # Try the newer way first (pyarrow >= 8.0.0)
                raise flight.FlightInternalError(error_msg, status_code=13)  # INTERNAL
            except (AttributeError, TypeError):
                # Fallback for older pyarrow versions
                raise flight.FlightInternalError(error_msg)


def main():
    """Start the Flight server with proper signal handling and cleanup."""
    import signal
    import sys
    
    # Get configuration from environment
    host = os.environ.get("FLIGHT_SERVER_HOST", "0.0.0.0")
    port = int(os.environ.get("FLIGHT_PORT", "50052"))
    
    # Create the server
    location = flight.Location.for_grpc_tcp(host, port)
    server = FlightServer(location=location)
    
    # Set up signal handlers for graceful shutdown
    def signal_handler(sig, frame):
        logger.info("Received shutdown signal, shutting down...")
        server.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the server
    logger.info(f"Starting Flight server on {host}:{port}")
    try:
        server.serve()  # Blocks until shutdown
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}", exc_info=True)
        raise
    finally:
        logger.info("Shutting down Flight server")
        server.shutdown()


if __name__ == "__main__":
    main()
